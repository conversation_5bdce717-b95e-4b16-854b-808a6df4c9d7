import type { Collection, Context, Reference, decimal, integer } from '@sage/xtrem-core';
import { Node, decorators, nanoIdDataType } from '@sage/xtrem-core';
import type * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremReporting from '@sage/xtrem-reporting';
import * as xtremUpload from '@sage/xtrem-upload';
import * as xtremTechnicalData from '../../index';

interface ComponentWithLevel {
    component: xtremTechnicalData.nodes.Component;
    level: integer;
    requiredQuantity: decimal;
    unit: xtremMasterData.nodes.UnitOfMeasure | null;
}

@decorators.node<BillOfMaterialPrintout>({
    package: 'xtrem-technical-data',
    storage: 'sql',
    isPublished: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
    indexes: [{ orderBy: { id: 1 }, isUnique: true }],
})
export class BillOfMaterialPrintout extends Node {
    @decorators.stringProperty<BillOfMaterialPrintout, 'id'>({
        isStored: true,
        isPublished: true,
        isRequired: true,
        dataType: () => nanoIdDataType,
    })
    readonly id: Promise<string>;

    @decorators.referenceProperty<BillOfMaterialPrintout, 'billOfMaterial'>({
        isStored: true,
        isPublished: true,
        node: () => xtremTechnicalData.nodes.BillOfMaterial,
        ignoreIsActive: true,
    })
    readonly billOfMaterial: Reference<xtremTechnicalData.nodes.BillOfMaterial>;

    @decorators.collectionProperty<BillOfMaterialPrintout, 'components'>({
        isPublished: true,
        node: () => xtremTechnicalData.nodes.ComponentPrintout,
        isVital: true,
        reverseReference: 'bomPrintout',
    })
    readonly components: Collection<xtremTechnicalData.nodes.ComponentPrintout>;

    private static async bomMultiLevelComponents(
        context: Context,
        bom: xtremTechnicalData.nodes.BillOfMaterial,
        baseQuantity: decimal,
        singleLevel: boolean,
        level?: integer,
    ): Promise<Array<ComponentWithLevel>> {
        let cpnTable: Array<ComponentWithLevel> = [];
        const cpnLvl = 1 + (!level || level < 0 ? 0 : level);
        const nbCpn1 = await bom.components.length;

        if (cpnLvl === 1) {
            const progress = await context.batch.getProgress();
            const { successCount } = progress;
            await context.batch.updateProgress({
                phase: 'start',
                totalCount: nbCpn1,
                errorCount: 0,
                detail: await bom.name,
                successCount,
            });
        }

        let i = 0;

        await bom.components
            .sort(async (cpn1, cpn2) => ((await cpn1.componentNumber) > (await cpn2.componentNumber) ? 1 : -1))
            .forEach(async component => {
                if (await context.batch.isStopRequested()) {
                    await context.batch.logMessage('warning', 'Stop requested');
                    await context.batch.confirmStop();
                    throw Error(
                        context.localize(
                            '@sage/xtrem-technical-data/nodes__bill_of_material_printout__printout_stopped',
                            'BOM printout stopped.',
                        ),
                    );
                }

                if (cpnLvl === 1) {
                    i += 1;
                    await context.batch.updateProgress({ phase: 'processing', successCount: i.valueOf() });
                    context.logger.info(
                        `${context.batch.trackingId} - ${i}/${nbCpn1} BOM printout mutation is running for id ${bom._id}`,
                    );
                }

                const unit = await component.unit; // Performance: Avoids awaiting a 2nd time.

                const requiredQuantity = xtremTechnicalData.functions.componentFunctions.setRequiredQuantity(
                    baseQuantity,
                    await (
                        await component.billOfMaterial
                    ).baseQuantity,
                    await component.linkQuantity,
                    await component.isFixedLinkQuantity,
                    await component.scrapFactor,
                    await unit?.decimalDigits,
                );

                cpnTable.push({
                    level: cpnLvl,
                    component,
                    requiredQuantity,
                    unit,
                });

                const subBom = await component.bom;
                if (subBom && !singleLevel) {
                    const subTable = await BillOfMaterialPrintout.bomMultiLevelComponents(
                        context,
                        subBom,
                        requiredQuantity,
                        singleLevel,
                        cpnLvl,
                    );
                    cpnTable = cpnTable.concat(subTable);
                }
            });

        if (cpnLvl === 1) await context.batch.updateProgress({ phase: 'finish', successCount: i });

        return cpnTable;
    }

    static async createBomPrintout(
        context: Context,
        billOfMaterial: xtremTechnicalData.nodes.BillOfMaterial,
        singleLevel = false,
        baseQuantity?: decimal,
    ): Promise<xtremTechnicalData.nodes.BillOfMaterialPrintout> {
        const createInWritableContext = async (writableContext: Context) => {
            const bomPrintout = await writableContext.create(xtremTechnicalData.nodes.BillOfMaterialPrintout, {
                id: writableContext.batch.trackingId,
                billOfMaterial,
                components: await BillOfMaterialPrintout.bomMultiLevelComponents(
                    context, // Use original context for read operations
                    billOfMaterial,
                    baseQuantity || (await billOfMaterial.baseQuantity),
                    singleLevel,
                ),
            });

            await bomPrintout.$.save();
            return bomPrintout;
        };

        if (context.isWritable) return createInWritableContext(context);
        return context.runInWritableContext(createInWritableContext);
    }

    @decorators.asyncMutation<typeof BillOfMaterialPrintout, 'generateBomReport'>({
        isPublished: true,
        startsReadOnly: true,
        parameters: [
            {
                name: 'bomId',
                type: 'string',
                isMandatory: true,
            },
            {
                name: 'singleLevel',
                type: 'boolean',
                isMandatory: false,
            },
            {
                name: 'omitCost',
                type: 'boolean',
                isMandatory: false,
            },
            {
                name: 'omitInstruction',
                type: 'boolean',
                isMandatory: false,
            },
            {
                name: 'skipReport',
                type: 'boolean',
                isMandatory: false,
            },
            {
                name: 'skipDelete',
                type: 'boolean',
                isMandatory: false,
            },
            {
                name: 'baseQuantity',
                type: 'decimal',
                isMandatory: false,
            },
        ],
        return: {
            isMandatory: false,
            type: 'reference',
            node: () => xtremUpload.nodes.UploadedFile,
        },
    })
    static async generateBomReport(
        context: Context,
        bomId: string,
        singleLevel = false,
        omitCost = false,
        omitInstruction = false,
        skipReport = false,
        skipDelete = false,
        baseQuantity?: decimal,
    ): Promise<xtremUpload.nodes.UploadedFile | null> {
        context.logger.info(`BOM printout mutation has started for id ${bomId}`);

        const bom = await context.read(xtremTechnicalData.nodes.BillOfMaterial, { _id: bomId });

        let bomName = await bom.name;
        if (bomName.length === 0) bomName = await (await bom.item).name;
        await context.batch.logMessage('info', bomName);

        await context.batch.logMessage('info', 'Mutation start');

        const bomPrintout = await BillOfMaterialPrintout.createBomPrintout(
            context,
            bom,
            singleLevel,
            baseQuantity || (await bom.baseQuantity),
        );

        await context.batch.logMessage('info', 'BOM printout data created');

        let report = null;
        if (!skipReport) {
            report = await xtremReporting.nodes.Report.generateUploadedFile(context, 'bomMultiLevel', '', {
                variables: JSON.stringify({
                    bomPrintoutId: await bomPrintout.id,
                    singleLevel: singleLevel ? 'true' : 'false',
                    omitCost: omitCost ? 'true' : 'false',
                    omitInstruction: omitInstruction ? 'true' : 'false',
                }),
            });
            await context.batch.logMessage('result', 'BOM printout data reported', { data: report });
        }

        if (!skipDelete) {
            // Use writable context for deletion as well
            const deleteInWritableContext = async (writableContext: Context) => {
                const printoutToDelete = await writableContext.read(xtremTechnicalData.nodes.BillOfMaterialPrintout, {
                    id: await bomPrintout.id,
                });
                await printoutToDelete.$.delete();
            };

            if (context.isWritable) {
                await deleteInWritableContext(context);
            } else {
                await context.runInWritableContext(deleteInWritableContext);
            }
            await context.batch.logMessage('info', 'BOM printout data deleted');
        }

        await context.batch.logMessage('info', 'Mutation end');
        context.logger.info(`BOM printout mutation has ended for id ${bomId}`);

        return report;
    }
}
