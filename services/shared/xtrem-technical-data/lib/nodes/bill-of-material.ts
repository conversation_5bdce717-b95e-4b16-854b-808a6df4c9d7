import type {
    BinaryStream,
    Collection,
    Context,
    Reference,
    ValidationContext,
    decimal,
    integer,
} from '@sage/xtrem-core';
import { Node, NodeStatus, asyncArray, date, decorators } from '@sage/xtrem-core';
import * as xtremMasterData from '@sage/xtrem-master-data';
import * as xtremSystem from '@sage/xtrem-system';
import * as xtremTechnicalData from '..';

@decorators.node<BillOfMaterial>({
    package: 'xtrem-technical-data',
    storage: 'sql',
    isPublished: true,
    canBulkDelete: true,
    canDeleteMany: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    canSearch: true,
    canRead: true,
    isCustomizable: true,
    canDuplicate: true,
    hasAttachments: true,
    indexes: [{ orderBy: { item: +1, site: +1 }, isUnique: true, isNaturalKey: true }],
    notifies: ['created', 'updated', 'deleted'],
    async controlEnd(cx) {
        const newStatus = await this.status;
        if (
            this.$.status === NodeStatus.modified &&
            newStatus === 'availableToUse' &&
            (await (await this.$.old).status) !== newStatus
        ) {
            await this.components
                .filter(async component => (await component.lineType) === 'normal')
                .forEach(async component => {
                    await xtremTechnicalData.events.control.componentLineControls.controlItemSiteExists(cx, component);
                });
        }
    },
    async controlDelete(cx: ValidationContext) {
        await xtremTechnicalData.events.control.bomControls.checkBomCanBeDeleted(cx, this);
    },
})
export class BillOfMaterial extends Node {
    /**
     * Flag to indicate if a revision is being deleted
     * It is used to accept the deletion of a component
     * that is linked to a BOM revision
     */
    @decorators.booleanProperty<BillOfMaterial, 'isDeletingRevision'>({
        defaultValue: false,
    })
    readonly isDeletingRevision: Promise<boolean>;

    @decorators.referenceProperty<BillOfMaterial, 'item'>({
        isStored: true,
        isPublished: true,
        node: () => xtremMasterData.nodes.Item,
        ignoreIsActive: true,
        // TODO: simplify it when a manufactured item could not be anymore non-stock managed
        filters: { control: { isManufactured: true, isStockManaged: true } },
        async control(cx, item) {
            await xtremMasterData.functions.controls.item.inactiveItemControl(cx, item);
        },
        lookupAccess: true,
        duplicateRequiresPrompt: true,
        duplicatedValue: null,
        isFrozen() {
            return xtremTechnicalData.events.frozen.bomPropertiesFrozen.isHeaderPropertyFrozen(this);
        },
    })
    readonly item: Reference<xtremMasterData.nodes.Item>;

    @decorators.referenceProperty<BillOfMaterial, 'site'>({
        isStored: true,
        isPublished: true,
        node: () => xtremSystem.nodes.Site,
        provides: ['site'],
        async control(cx, val) {
            await xtremSystem.events.control.isActive(
                this.$.status,
                cx,
                this.$.status === NodeStatus.modified ? (await this.$.old)._id : null,
                val,
            );
        },
        filters: { control: { isManufacturing: true } },
        lookupAccess: true,
        duplicateRequiresPrompt: true,
        isFrozen() {
            return xtremTechnicalData.events.frozen.bomPropertiesFrozen.isHeaderPropertyFrozen(this);
        },
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.stringProperty<BillOfMaterial, 'name'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremSystem.dataTypes.localizedName,
        dependsOn: ['item'],
        async defaultValue() {
            return (await this.item).name;
        },
        lookupAccess: true,
        duplicateRequiresPrompt: true,
    })
    readonly name: Promise<string>;

    @decorators.booleanProperty<BillOfMaterial, 'isActive'>({
        isPublished: true,
        lookupAccess: true,
        async getValue() {
            return (await this.status) !== 'inDevelopment';
        },
        provides: ['isActive'],
    })
    readonly isActive: Promise<boolean>;

    @decorators.enumProperty<BillOfMaterial, 'status'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremTechnicalData.enums.availabilityDataType,
        defaultValue: () => 'inDevelopment',
        duplicateRequiresPrompt: true,
        async control(cx) {
            if (await (await this.item).isBomRevisionManaged) {
                await xtremTechnicalData.events.control.bomControls.validateBomStatusChange(cx, this);
            }
            await xtremTechnicalData.events.control.bomControls.validateItemSiteForAvailableToUseBom(cx, this);
        },
    })
    readonly status: Promise<xtremTechnicalData.enums.Availability>;

    @decorators.decimalProperty<BillOfMaterial, 'baseQuantity'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        dependsOn: ['itemSite'],
        async defaultValue() {
            return (await (await this.itemSite)?.batchQuantity) ?? 0;
        },
        async control(cx, val) {
            await cx.error.if(val).is.not.greater.than(0);
        },
        isFrozen() {
            return xtremTechnicalData.events.frozen.bomPropertiesFrozen.isHeaderPropertyFrozen(this);
        },
    })
    readonly baseQuantity: Promise<decimal>;

    @decorators.decimalProperty<BillOfMaterial, 'componentQuantity'>({
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.quantityInStockUnit,
        isTransientInput: true,
    })
    readonly componentQuantity: Promise<decimal>;

    @decorators.referenceProperty<BillOfMaterial, 'currency'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: ['site'],
        lookupAccess: true,
        async getValue() {
            if (await this.site) {
                return (await (await this.site).legalCompany).currency;
            }
            return null;
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency | null>;

    @decorators.decimalProperty<BillOfMaterial, 'standardCost'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremMasterData.dataTypes.costDataType,
        dependsOn: [{ components: ['standardCost'] }],
        getValue() {
            return this.components.where(async cmp => (await cmp.revision) == null).sum(cmp => cmp.standardCost);
        },
    })
    readonly standardCost: Promise<decimal>;

    @decorators.referenceProperty<BillOfMaterial, 'stockUnit'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremMasterData.nodes.UnitOfMeasure,
        async getValue() {
            return (await this.item).stockUnit;
        },
    })
    readonly stockUnit: Reference<xtremMasterData.nodes.UnitOfMeasure>;

    @decorators.binaryStreamProperty<BillOfMaterial, 'image'>({
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        async getValue() {
            return (await this.item).image;
        },
    })
    readonly image: Promise<BinaryStream | null>;

    @decorators.referenceProperty<BillOfMaterial, 'routing'>({
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        node: () => xtremTechnicalData.nodes.Routing,
        dependsOn: ['site', 'item'],
        join: {
            item() {
                return this.item;
            },
            site() {
                return this.site;
            },
        },
    })
    readonly routing: Reference<xtremTechnicalData.nodes.Routing | null>;

    @decorators.collectionProperty<BillOfMaterial, 'components'>({
        isPublished: true,
        lookupAccess: true,
        // in case of new revision, the new revision number calculated in the BillOfMaterialRevision node
        // will be applied to the related components => components depends on revisions
        dependsOn: ['revisions'],
        node: () => xtremTechnicalData.nodes.Component,
        isVital: true,
        reverseReference: 'billOfMaterial',
    })
    readonly components: Collection<xtremTechnicalData.nodes.Component>;

    @decorators.collectionProperty<BillOfMaterial, 'revisions'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremTechnicalData.nodes.BillOfMaterialRevision,
        isVital: true,
        reverseReference: 'billOfMaterial',
    })
    readonly revisions: Collection<xtremTechnicalData.nodes.BillOfMaterialRevision>;

    @decorators.collectionProperty<BillOfMaterial, 'invertedRevisions'>({
        node: () => xtremTechnicalData.nodes.BillOfMaterialRevision,
        join: {
            billOfMaterial() {
                return this;
            },
        },
        orderBy: { startDate: -1, statusOrder: 1, _id: -1 },
    })
    readonly invertedRevisions: Collection<xtremTechnicalData.nodes.BillOfMaterialRevision>;

    @decorators.referenceProperty<BillOfMaterial, 'currentRevision'>({
        isPublished: true,
        isNullable: true,
        lookupAccess: true,
        node: () => xtremTechnicalData.nodes.BillOfMaterialRevision,
        getValue() {
            return xtremTechnicalData.functions.bomRevisionLib.getRevisionByDate(this, date.today());
        },
    })
    readonly currentRevision: Reference<xtremTechnicalData.nodes.BillOfMaterialRevision | null>;

    @decorators.dateProperty<BillOfMaterial, 'validTo'>({
        isPublished: true,
        isNullable: true,
        async getValue() {
            const firstRevision = await this.revisions.takeOne(revision => revision != null);
            if (firstRevision == null) {
                return null;
            }
            return (await firstRevision.startDate).addDays(-1);
        },
    })
    readonly validTo: Promise<date | null>;

    @decorators.dateProperty<BillOfMaterial, 'currentValidTo'>({
        isPublished: true,
        isNullable: true,
        async getValue() {
            const currentRevision = await this.currentRevision;
            if (currentRevision == null) {
                return this.validTo;
            }
            return currentRevision.validTo;
        },
    })
    readonly currentValidTo: Promise<date | null>;

    @decorators.enumProperty<BillOfMaterial, 'currentStatus'>({
        isPublished: true,
        lookupAccess: true,
        dataType: () => xtremTechnicalData.enums.availabilityDataType,
        async getValue() {
            const currentStatus = (await (await this.currentRevision)?.status) ?? this.status;
            return currentStatus;
        },
    })
    readonly currentStatus: Promise<xtremTechnicalData.enums.Availability>;

    /**
     * The last revision number generated with the BOM revision sequence
     */
    @decorators.stringProperty<BillOfMaterial, 'lastRevisionNumber'>({
        isStoredOutput: true,
        isPublished: true,
        lookupAccess: true,
        serviceOptions: () => [xtremMasterData.serviceOptions.billOfMaterialRevisionServiceOption],
        dataType: () => xtremSystem.dataTypes.id,
        dependsOn: ['item', 'revisions'],
        defaultValue: '',
        async updatedValue() {
            // If the BOM revision sequence number is not set, we don't want to update the last revision number
            if (!(await (await this.item).bomRevisionSequenceNumber)) return '';

            const length = await this.revisions.length;
            const lastRevision = await this.revisions.at(length - 1);
            return (await lastRevision?.revision) ?? '';
        },
    })
    readonly lastRevisionNumber: Promise<string>;

    @decorators.collectionProperty<BillOfMaterial, 'trackings'>({
        isPublished: true,
        lookupAccess: true,
        node: () => xtremTechnicalData.nodes.BillOfMaterialTracking,
        reverseReference: 'billOfMaterial',
    })
    readonly trackings: Collection<xtremTechnicalData.nodes.BillOfMaterialTracking>;

    @decorators.referenceProperty<BillOfMaterial, 'itemSite'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.ItemSite,
        isNullable: true,
        dependsOn: ['item', 'site'],
        lookupAccess: true,
        join: {
            item() {
                return this.item;
            },
            site() {
                return this.site;
            },
        },
    })
    readonly itemSite: Reference<xtremMasterData.nodes.ItemSite | null>;

    @decorators.integerProperty<BillOfMaterial, 'prodLeadTime'>({
        isPublished: true,
        dependsOn: ['item', 'site'],
        async getValue() {
            return (await (await this.itemSite)?.prodLeadTime) ?? 0;
        },
        lookupAccess: true,
    })
    readonly prodLeadTime: Promise<integer>;

    @decorators.query<typeof BillOfMaterial, 'getLowLevelCode'>({
        isPublished: true,
        parameters: [
            { name: 'itemId', type: 'integer', isMandatory: true },
            { name: 'siteId', type: 'integer', isMandatory: false },
        ],
        return: { type: 'integer' },
    })
    static async getLowLevelCode(context: Context, itemId: integer, siteId: integer): Promise<integer> {
        const parents: xtremTechnicalData.nodes.Component[] = [];

        // REFACTOR
        //
        // At the moment filtering for a property of a jsonProperty is not possible (see XT-43704).
        // So, we have to filter later in the array. As soon as this restriction has been lifted
        // we should do the filtering on the query instead (performance!).
        await context.queryWithReader(
            xtremTechnicalData.nodes.Component,
            { filter: {} },
            // filter: {
            //     _and: [{ item: { id: itemId } }, { billOfMaterial: { site: { id: siteId } } }],
            // },
            async reader => {
                await reader.forEach(async bomLine => {
                    // REFACTOR BEGIN: As soon as XT-43704 is fullfilled remove!
                    if (
                        (await bomLine.item)?._id !== itemId ||
                        (await (await bomLine.billOfMaterial).site)._id !== siteId
                    ) {
                        return;
                    }
                    // REFACTOR END

                    parents.push(bomLine);
                });
            },
        );

        if (parents.length === 0) {
            return 0;
        }

        return (
            1 +
            Math.max(
                ...(await asyncArray(parents)
                    .uniq()
                    .map(async element =>
                        BillOfMaterial.getLowLevelCode(
                            context,
                            (await (await element.billOfMaterial).item)._id,
                            siteId,
                        ),
                    )
                    .toArray()),
            )
        );
    }

    @decorators.bulkMutation<typeof BillOfMaterial, 'bulkPrint'>({
        isPublished: true,
        queue: 'reporting',
        startsReadOnly: true,
        parameters: [
            { name: 'multilevel', type: 'boolean', isMandatory: false },
            { name: 'cost', type: 'boolean', isMandatory: false },
            { name: 'instruction', type: 'boolean', isMandatory: false },
        ],
        async onComplete(context, reports) {
            const reportName = context.localize(
                '@sage/xtrem-technical-data/node__bill_of_material_bulk_print_report_name',
                'Bill of material',
            );
            await xtremMasterData.functions.bulkPrintOnCompleteHandler(context, {
                documentType: 'bomMultiLevel',
                documents: reports,
                reportName,
            });
        },
    })
    static async bulkPrint(
        context: Context,
        bom: BillOfMaterial,
        multilevel = true,
        cost = false,
        instruction = false,
    ) {
        const report = await xtremTechnicalData.nodes.BillOfMaterialPrintout.generateBomReport(
            context,
            bom._id.toString(),
            !multilevel,
            !cost,
            !instruction,
        );
        if (report !== null) {
            return report;
        }
        throw new Error('No report');
    }

    @decorators.asyncMutation<typeof BillOfMaterial, 'createTestBillsOfMaterial'>({
        isPublished: true,
        startsReadOnly: false,
        serviceOptions: () => [xtremSystem.serviceOptions.DevTools],
        parameters: [
            {
                isMandatory: false,
                name: 'parameters',
                type: 'object',
                properties: {
                    bomItemId: {
                        name: 'bomItemId',
                        type: 'string',
                        isMandatory: true,
                    },
                    componentItemId: {
                        name: 'componentItemId',
                        type: 'string',
                        isMandatory: true,
                    },
                    itemQuantity: {
                        name: 'itemQuantity',
                        type: 'integer',
                        isMandatory: true,
                    },
                    bomQuantity: {
                        name: 'bomQuantity',
                        type: 'integer',
                        isMandatory: true,
                    },
                    componentQuantity: {
                        name: 'componentQuantity',
                        type: 'integer',
                        isMandatory: true,
                    },
                    numberOfLinesPerBom: {
                        name: 'numberOfLinesPerBom',
                        type: 'integer',
                        isMandatory: true,
                    },
                    fixedNumberOfLines: {
                        name: 'fixedNumberOfLines',
                        type: 'boolean',
                        isMandatory: false,
                    },
                },
            },
        ],
        return: {
            type: 'string',
        },
    })
    static async createTestBillsOfMaterial(
        context: Context,
        parameters: {
            bomItemId: string;
            componentItemId: string;
            itemQuantity: integer;
            bomQuantity: integer;
            componentQuantity: integer;
            numberOfLinesPerBom: integer;
            fixedNumberOfLines?: boolean;
        },
    ) {
        const item = await context.read(xtremMasterData.nodes.Item, {
            id: parameters.bomItemId,
        });

        const site = await (await item.itemSites.at(0))?.site;

        for (let i = 1; i <= parameters.bomQuantity; i += 1) {
            if (parameters.bomQuantity > parameters.itemQuantity) {
                return `Number of items created (not enough items for the requested quantity) - ${i - 1}`;
            }
            let lineNumber = 1;
            if (parameters.fixedNumberOfLines) {
                lineNumber = parameters.numberOfLinesPerBom;
            } else {
                lineNumber = xtremMasterData.functions.randomInteger(1, parameters.numberOfLinesPerBom);
            }
            let itemNumber = xtremMasterData.functions.randomInteger(1, parameters.componentQuantity);
            let lineItem = await context.read(xtremMasterData.nodes.Item, {
                id: `${parameters.componentItemId}-${itemNumber}`,
            });
            const bomItem = await context.read(xtremMasterData.nodes.Item, {
                id: `${parameters.bomItemId}-${i}`,
            });
            const newOrder = await context.create(BillOfMaterial, {
                item: bomItem,
                site,
                baseQuantity: 1,
                status: 'availableToUse',
                components: [
                    {
                        componentNumber: 10,
                        item: lineItem,
                        linkQuantity: xtremMasterData.functions.randomInteger(1, 20),
                        unit: await lineItem.stockUnit,
                    },
                ],
            });
            if (lineNumber > 1) {
                for (let l = 2; l < lineNumber; l += 1) {
                    itemNumber = xtremMasterData.functions.randomInteger(1, parameters.componentQuantity);
                    lineItem = await context.read(xtremMasterData.nodes.Item, {
                        id: `${parameters.componentItemId}-${itemNumber}`,
                    });
                    await newOrder.components.append({
                        componentNumber: l * 10,
                        item: lineItem,
                        linkQuantity: xtremMasterData.functions.randomInteger(1, 20),
                        unit: await lineItem.stockUnit,
                    });
                }
            }

            await newOrder.$.save();
        }
        return `Number of items created - ${parameters.bomQuantity}`;
    }

    @decorators.asyncMutation<typeof BillOfMaterial, 'checkForCircularBOM'>({
        isPublished: true,
        startsReadOnly: true,
        isSchedulable: true,
        parameters: [
            {
                name: 'bomId',
                type: 'string',
                isMandatory: false,
            },
        ],
        return: {
            type: 'string',
        },
    })
    static async checkForCircularBOM(context: Context, bomId: string) {
        let failed = 0;
        let bomIds: string[] = [];

        if (bomId) {
            bomIds = [bomId];
        } else {
            bomIds = (
                await context.select(
                    xtremTechnicalData.nodes.BillOfMaterial,
                    {
                        _id: true,
                    },
                    {
                        filter: {
                            status: { _in: ['availableToUse', 'inDevelopment'] },
                        },
                    },
                )
            ).map(bom => bom._id.toString());
        }
        const bomCount = bomIds.length;
        await context.batch.logMessage(
            'error',
            context.localize(
                '@sage/xtrem-technical-data/starting_circular_bom_check',
                'Starting the check for circular references. Number of BOMs to check: {{bomCount}}.',
                { bomCount },
            ),
        );
        await context.batch.updateProgress({
            detail: 'start',
            errorCount: failed,
            successCount: bomCount - failed,
            totalCount: bomCount,
            phase: 'starting',
        });
        await asyncArray(bomIds).forEach(async checkingBomId => {
            const circularBom = await xtremTechnicalData.functions.bomLib.checkForCircularBom(context, checkingBomId);
            if (circularBom) {
                const failedBom = await context.read(xtremTechnicalData.nodes.BillOfMaterial, {
                    _id: checkingBomId,
                });
                failed += 1;
                await context.batch.logMessage(
                    'error',
                    context.localize(
                        '@sage/xtrem-technical-data/circular_bom_found',
                        'There is a circular reference in the BOM: {{bom}}.',
                        { bom: `${await (await failedBom.item).id}-${await (await failedBom.site).id}` },
                    ),
                );
            }
        });
        await context.batch.updateProgress({
            detail: 'complete',
            errorCount: failed,
            successCount: bomCount - failed,
            totalCount: bomCount,
            phase: 'done',
        });
        if (failed) {
            throw new Error(`Number of BOMs failed - ${failed}`);
        }
        await context.batch.logMessage(
            'error',
            context.localize(
                '@sage/xtrem-technical-data/finished_circular_bom_check',
                'The circular BOM check is complete. Number of BOMs: checked {{bomCount}}, failed {{failed}}.',
                { failed, bomCount },
            ),
        );
        return `Number of BOMs processed - ${bomCount}`;
    }

    /**
     * Needed to only delete the current revision of a BOM instead of the BOM itself
     */
    @decorators.bulkMutation<typeof BillOfMaterial, 'bulkDeleteWithRevisions'>({
        isPublished: true,
        async onComplete(context, billOfMaterialCodes) {
            const numbers = billOfMaterialCodes.join(', ');
            await context.batch.logMessage(
                'info',
                context.localize(
                    '@sage/xtrem-technical-data/bom-revision-lib__bulk_delete_with_revision__bom_bulk_deletion_completed',
                    'BOM bulk deletion completed',
                ),
            );

            await context.notifyUser({
                title: context.localize(
                    '@sage/xtrem-technical-data/nodes__bill_of_material__bill_of_materials_deleted',
                    'Bill of material deleted',
                ),
                icon: 'delete',
                description: numbers,
                level: 'success',
                shouldDisplayToast: true,
                actions: [
                    {
                        link: context.batch.notificationStateLink,
                        title: 'History',
                        icon: 'link',
                        style: 'tertiary',
                    },
                ],
            });
        },
    })
    static async bulkDeleteWithRevisions(context: Context, billOfMaterial: BillOfMaterial) {
        await xtremTechnicalData.functions.bomRevisionLib.deleteCurrentRevisionOrBom(context, billOfMaterial);
    }
}
