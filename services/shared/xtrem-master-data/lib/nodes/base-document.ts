import type { AsyncArrayReader, Collection, Context, Reference, integer } from '@sage/xtrem-core';
import {
    BusinessRuleError,
    Logger,
    Node,
    NodeStatus,
    TextStream,
    date,
    decorators,
    useDefaultValue,
} from '@sage/xtrem-core';
import * as xtremSystem from '@sage/xtrem-system';
import * as _ from 'lodash';
import * as xtremMasterData from '..';
import {
    cannotUpdateClosedDocument,
    cannotUpdateDocumentCurrency,
    cannotUpdateDocumentDate,
} from '../events/control/base-document';
import type { ApprovalRequestMail } from '../interfaces/approval-request-mail';

const logger = Logger.getLogger(__filename, 'base-document');

@decorators.node<BaseDocument>({
    isClearedByReset: true,
    storage: 'sql',
    canRead: true,
    canCreate: true,
    canUpdate: true,
    canDelete: true,
    isPublished: true,
    isAbstract: true,
    isCustomizable: true,
    canDuplicate: true,
    indexes: [{ orderBy: { _constructor: 1, number: 1 }, isUnique: true, isNaturalKey: true }],
    notifies: ['created', 'updated', 'deleted'],
    prepareBegin() {
        if (this.$.status === NodeStatus.added) {
            if (!this.$.isValueDeferred('number')) {
                logger.warn(() =>
                    this.$.context.localize(
                        '@sage/xtrem-master-data/nodes__base_document__id_already_exists',
                        'The ID already exists. No sequence number will be allocated to the current  document.',
                    ),
                );
            }
        }
    },
    async controlBegin(cx) {
        if (this.$.status === NodeStatus.modified) {
            await cannotUpdateClosedDocument(cx, this);
            await cannotUpdateDocumentDate(cx, this);
            await cannotUpdateDocumentCurrency(cx, this);
        }
    },
    async controlEnd(cx) {
        await (await this.getDocumentNumberGenerator())?.controls(cx);
    },
    createEnd() {
        this.isBeingCreatedOrDeleted = true;
    },
    async saveEnd() {
        await this.updateLinkedDocument();
    },
    deleteBegin() {
        this.isBeingCreatedOrDeleted = true;
    },
    async deleteEnd() {
        await this.updateLinkedDocument();
    },
})
export class BaseDocument extends Node {
    isBeingCreatedOrDeleted = false;

    linesToNotify: xtremMasterData.interfaces.DocumentLineToNotify[] = [];

    documentNumberGenerator: xtremMasterData.classes.DocumentNumberGenerator | null = null;

    getStockSite() {
        return this.stockSite;
    }

    getEffectiveDate() {
        return this.date;
    }

    async getDocumentNumberGenerator() {
        if (this.documentNumberGenerator) {
            return this.documentNumberGenerator;
        }
        this.documentNumberGenerator = await xtremMasterData.classes.DocumentNumberGenerator.create(this.$.context, {
            nodeInstance: this,
            skipControls: true,
        });
        return this.documentNumberGenerator;
    }

    @decorators.stringProperty<BaseDocument, 'number'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.documentNumber,
        provides: ['sequenceNumber'],
        lookupAccess: true,
        duplicatedValue: useDefaultValue,
        async isFrozen() {
            if ((await this.status) === 'posted') {
                return true;
            }
            const notFrozen = (await (await this.getDocumentNumberGenerator())?.isTemporaryNumber()) ?? false;
            return !notFrozen;
        },
        defaultValue() {
            return xtremMasterData.functions.getDocumentNumberPlaceholder();
        },
        async deferredDefaultValue() {
            return (await this.getDocumentNumberGenerator())?.allocate() ?? '';
        },
    })
    readonly number: Promise<string>;

    @decorators.enumProperty<BaseDocument, 'status'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.enums.baseStatusDataType,
        defaultValue: 'draft',
        lookupAccess: true,
        duplicatedValue: useDefaultValue,
    })
    readonly status: Promise<xtremMasterData.enums.BaseStatus>;

    @decorators.enumProperty<BaseDocument, 'displayStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.enums.baseDisplayStatusDataType,
        defaultValue: 'draft',
        lookupAccess: true,
        duplicatedValue: useDefaultValue,
    })
    readonly displayStatus: Promise<xtremMasterData.enums.BaseDisplayStatus>;

    @decorators.enumProperty<BaseDocument, 'approvalStatus'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.enums.approvalStatusDataType,
        defaultValue: 'draft',
        lookupAccess: true,
        duplicatedValue: useDefaultValue,
    })
    readonly approvalStatus: Promise<xtremMasterData.enums.ApprovalStatus>;

    @decorators.dateProperty<BaseDocument, 'date'>({
        isStored: true,
        isPublished: true,
        defaultValue() {
            return date.today();
        },
        duplicatedValue: useDefaultValue,
    })
    readonly date: Promise<date>;

    /** Needed for the accounting interface */
    @decorators.dateProperty<BaseDocument, 'documentDate'>({
        provides: ['documentDate'],
        dependsOn: ['date'],
        getValue() {
            return this.date;
        },
    })
    readonly documentDate: Promise<date>;

    @decorators.booleanProperty<BaseDocument, 'canPrint'>({
        isPublished: true,
        lookupAccess: true,
        getValue() {
            return false;
        },
    })
    readonly canPrint: Promise<boolean>;

    @decorators.booleanProperty<BaseDocument, 'isPrinted'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        defaultValue: false,
        duplicatedValue: false,
    })
    readonly isPrinted: Promise<boolean>;

    canUpdateIsPrinted = false;

    @decorators.booleanProperty<BaseDocument, 'isSent'>({
        isPublished: true,
        lookupAccess: true,
        isStored: true,
        defaultValue: false,
        duplicatedValue: false,
    })
    readonly isSent: Promise<boolean>;

    @decorators.referenceProperty<BaseDocument, 'site'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        provides: ['site'],
        dataType: () => xtremMasterData.dataTypes.masterDataSite,
        node: () => xtremSystem.nodes.Site,
    })
    readonly site: Reference<xtremSystem.nodes.Site>;

    @decorators.referenceProperty<BaseDocument, 'stockSite'>({
        isStored: true,
        isNullable: true,
        isPublished: true,
        lookupAccess: true,
        dependsOn: ['site'],
        filters: {
            controls: [
                {
                    filter: { isInventory: true },
                    getErrorMessage() {
                        return this.$.context.localize(
                            '@sage/xtrem-master-data/nodes__base_document__site_is_not_inventory',
                            'The site needs to be a stock site.',
                        );
                    },
                },
                {
                    filter: {
                        async legalCompany() {
                            return (await (await this.site)?.legalCompany)?._id;
                        },
                    },
                    getErrorMessage() {
                        return this.$.context.localize(
                            '@sage/xtrem-master-data/nodes__base_document__stock_site_legal_company_mismatch',
                            'The site needs to belong to the same legal company.',
                        );
                    },
                },
            ],
        },
        node: () => xtremSystem.nodes.Site,
        dataType: () => xtremMasterData.dataTypes.masterDataSite,
        async defaultValue() {
            return (await this.site).stockSite;
        },
    })
    readonly stockSite: Reference<xtremSystem.nodes.Site | null>;

    @decorators.referenceProperty<BaseDocument, 'currency'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        dependsOn: ['site'],
        node: () => xtremMasterData.nodes.Currency,
        dataType: () => xtremMasterData.dataTypes.currency,
        isFrozen() {
            return this.$.status === NodeStatus.modified;
        },
        async defaultValue() {
            if (!(await this.site)) return null;
            return (await this.site).currency;
        },
    })
    readonly currency: Reference<xtremMasterData.nodes.Currency>;

    /** Needed for the accounting interface  */
    @decorators.referenceProperty<BaseDocument, 'transactionCurrency'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        dataType: () => xtremMasterData.dataTypes.currency,
        dependsOn: ['currency'],
        getValue() {
            return this.currency;
        },
    })
    readonly transactionCurrency: Reference<xtremMasterData.nodes.Currency>;

    /** needed for finance engine  */
    @decorators.referenceProperty<BaseDocument, 'companyCurrency'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Currency,
        dependsOn: ['site'],
        async getValue() {
            return (await (await this.site).legalCompany).currency;
        },
    })
    readonly companyCurrency: Reference<xtremMasterData.nodes.Currency>;

    @decorators.referenceProperty<BaseDocument, 'financialSite'>({
        isStored: true,
        isPublished: true,
        lookupAccess: true,
        ignoreIsActive: true,
        dependsOn: ['site'],
        node: () => xtremSystem.nodes.Site,
        dataType: () => xtremMasterData.dataTypes.masterDataSite,

        filters: {
            controls: [
                {
                    filter: { isFinance: true },
                    getErrorMessage() {
                        return this.$.context.localize(
                            '@sage/xtrem-master-data/nodes__base_document__financial_site_is_not_financial',
                            'The site needs to be a financial site.',
                        );
                    },
                },
            ],
        },
        async defaultValue() {
            const site = await this.site;
            if (!site) return null;
            if (await site.isFinance) return site;
            const financialSite = await site.financialSite;
            if (financialSite) return financialSite;
            throw new BusinessRuleError(
                this.$.context.localize(
                    '@sage/xtrem-master-data/nodes__base_document__no_financial_site',
                    'No financial site found for the current site.',
                ),
            );
        },
        duplicatedValue: useDefaultValue,
    })
    readonly financialSite: Reference<xtremSystem.nodes.Site>;

    @decorators.textStreamProperty<BaseDocument, 'text'>({
        isStored: true,
        isPublished: true,
        dataType: () => xtremMasterData.dataTypes.note,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'fixed',
        anonymizeValue: TextStream.fromString('*'.repeat(15)),
        duplicatedValue: useDefaultValue,
        defaultValue: TextStream.empty,
    })
    readonly text: Promise<TextStream>;

    /** One day can be move into a child node or a collection if we want to manage multiple NOTES  */
    @decorators.textStreamProperty<BaseDocument, 'internalNote'>({
        isPublished: true,
        isStored: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'fixed',
        anonymizeValue: TextStream.fromString('*'.repeat(15)),
        dataType: () => xtremMasterData.dataTypes.note,
        duplicatedValue: useDefaultValue,
        defaultValue: TextStream.empty,
    })
    readonly internalNote: Promise<TextStream>;

    @decorators.textStreamProperty<BaseDocument, 'externalNote'>({
        isPublished: true,
        isStored: true,
        dataSensitivityLevel: 'personal',
        anonymizeMethod: 'fixed',
        anonymizeValue: TextStream.fromString('*'.repeat(15)),
        dataType: () => xtremMasterData.dataTypes.note,
        duplicatedValue: useDefaultValue,
        defaultValue: TextStream.empty,
        async control(cx) {
            await xtremMasterData.events.control.externalNoteControl.externalNoteEmpty(
                await this.isExternalNote,
                await this.externalNote,
                cx,
            );
        },
    })
    readonly externalNote: Promise<TextStream>;

    @decorators.booleanProperty<BaseDocument, 'isExternalNote'>({
        isPublished: true,
        isStored: true,
        defaultValue: false,
        duplicatedValue: useDefaultValue,
    })
    readonly isExternalNote: Promise<boolean>;

    @decorators.booleanProperty<BaseDocument, 'isOverwriteNote'>({
        isPublished: true,
        isTransientInput: true,
    })
    readonly isOverwriteNote: Promise<boolean>;

    @decorators.booleanProperty<BaseDocument, 'isTransferHeaderNote'>({
        isPublished: true,
        isStored: true,
        defaultValue: false,
        duplicatedValue: useDefaultValue,
    })
    readonly isTransferHeaderNote: Promise<boolean>;

    @decorators.booleanProperty<BaseDocument, 'isTransferLineNote'>({
        isPublished: true,
        isStored: true,
        defaultValue: false,
        duplicatedValue: useDefaultValue,
    })
    readonly isTransferLineNote: Promise<boolean>;

    @decorators.collectionProperty<BaseDocument, 'lines'>({
        isPublished: true,
        isVital: true,
        reverseReference: 'document',
        dependsOn: ['site'],
        node: () => xtremMasterData.nodes.BaseDocumentItemLine,
    })
    readonly lines: Collection<xtremMasterData.nodes.BaseDocumentItemLine>;

    @decorators.referenceProperty<BaseDocument, 'siteAddress'>({
        isPublished: true,
        node: () => xtremMasterData.nodes.Address,
        isStored: true,
        isMutable: true,
        isNullable: true,
        dependsOn: ['site'],
        async defaultValue() {
            const primarySiteAddress = await (await this.site).primaryAddress;
            if (primarySiteAddress) {
                return primarySiteAddress.address;
            }
            const bePrimarySiteAddress = await (await (await this.site).businessEntity).primaryAddress;
            if (bePrimarySiteAddress) {
                return bePrimarySiteAddress.address;
            }
            return null;
        },
        updatedValue: useDefaultValue,
    })
    readonly siteAddress: Reference<xtremMasterData.nodes.Address | null>;

    @decorators.referenceProperty<BaseDocument, 'businessEntityAddress'>({
        isStored: true,
        isPublished: true,
        isNullable: true,
        node: () => xtremMasterData.nodes.BusinessEntityAddress,
        dependsOn: ['site'],
        filters: {
            lookup: {
                async businessEntity(): Promise<number> {
                    return (await (await this.site).businessEntity)._id;
                },
            },
        },
        async defaultValue() {
            return (await this.site).primaryAddress;
        },
    })
    readonly businessEntityAddress: Reference<xtremMasterData.nodes.BusinessEntityAddress | null>;

    async beforeSendApprovalRequestMail(context: Context, user: xtremSystem.nodes.User): Promise<ApprovalRequestMail> {
        await logger.errorAsync(
            async () =>
                `This method must be implemented in the child class - ${this.$.constructor.name} - ${await user.email}`,
        );
        throw new BusinessRuleError(
            context.localize(
                '@sage/xtrem-master-data/node__base_document__no_validation_email_allowed',
                'There is no email validation allowed {{document}}',
                { document: this.$.factory.getLocalizedTitle(this.$.context) },
            ),
        );
    }

    /** Page url of the document TODO get this from the datatype // tunnelPAge of the document??  */
    @decorators.stringProperty<BaseDocument, 'page'>({ dataType: () => xtremSystem.dataTypes.url, getValue: () => '' })
    readonly page: Promise<string>;

    @decorators.stringProperty<BaseDocument, 'documentUrl'>({
        dataType: () => xtremSystem.dataTypes.url,
        async computeValue() {
            return `${await this.page}/${Buffer.from(JSON.stringify({ _id: this._id.toString() })).toString('base64')}`;
        },
    })
    readonly documentUrl: Promise<string>;

    @decorators.stringProperty<BaseDocument, 'approvalPage'>({
        dataType: () => xtremSystem.dataTypes.url,
        getValue: () => '',
    })
    readonly approvalPage: Promise<string>;

    @decorators.stringProperty<BaseDocument, 'approvalUrl'>({
        dataType: () => xtremSystem.dataTypes.url,
        async computeValue() {
            return `${await this.approvalPage}/${Buffer.from(JSON.stringify({ _id: this._id.toString() })).toString('base64')}`;
        },
    })
    readonly approvalUrl: Promise<string>;

    // Move on stock-data extension ?
    @decorators.booleanProperty<BaseDocument, 'forceUpdateForStock'>({
        defaultValue: false,
    })
    readonly forceUpdateForStock: Promise<boolean>;

    @decorators.booleanProperty<BaseDocument, 'forceUpdateForResync'>({
        defaultValue: false,
    })
    readonly forceUpdateForResync: Promise<boolean>;

    @decorators.booleanProperty<BaseDocument, 'canUpdateClosedDocument'>({
        defaultValue: false,
    })
    readonly canUpdateClosedDocument: Promise<boolean>;

    /**
     * Operation that send an approval email to a user email address.
     * If no email address is provided, it must be defaulted, if there is no user found, then an error is raised
     * @param context
     * @param stockTransferReturnRequestNumber
     * @param email
     */
    @decorators.mutation<typeof BaseDocument, 'sendApprovalRequestMail'>({
        isPublished: true,
        parameters: [
            { name: 'document', type: 'reference', node: () => BaseDocument, isWritable: true },
            {
                name: 'user',
                type: 'instance',
                node: () => xtremSystem.nodes.User,
                isNullable: true,
                isTransientInput: true,
            },
        ],
        return: 'boolean',
    })
    static async sendApprovalRequestMail(
        context: Context,
        document: BaseDocument,
        user: xtremSystem.nodes.User,
    ): Promise<boolean> {
        const { mailerUser, ...mail } = await document.beforeSendApprovalRequestMail(context, user);

        await xtremMasterData.functions.sendMail(context, { mailerUser, mail }, document);

        await document.$.set({ approvalStatus: 'pendingApproval' });
        await document.$.save();

        return true;
    }

    @decorators.mutation<typeof BaseDocument, 'setIsPrintedIsSent'>({
        isPublished: true,
        parameters: [
            { name: 'document', type: 'reference', node: () => BaseDocument, isMandatory: true, isWritable: true },
            { name: 'send', type: 'boolean', isMandatory: false },
        ],
        return: { type: 'boolean' },
    })
    static async setIsPrintedIsSent(context: Context, document: BaseDocument, send: boolean = false): Promise<boolean> {
        const exec = async (writable: Context) => {
            await writable.bulkUpdate(xtremMasterData.nodes.BaseDocument, {
                // Fast, idempotent flag flip via bulkUpdate (skips full controls/validation + aggregate load)
                set: { isSent: send, isPrinted: true },
                where: { _id: { _eq: document._id }, isPrinted: { _eq: false } },
            });
        };
        if (context.isWritable) await exec(context);
        else await context.runInWritableContext(exec);
        return true;
    }

    /**
     * transform linesToNotify to a group of lines by constructor and remove duplicates
     * example: [{documentNodeName: 'PurchaseOrder', document: 110, line: 1111 },
     *           {documentNodeName: 'PurchaseOrder', document: 110, line: 5555 },
     *           {documentNodeName: 'PurchaseOrder', document: 220, line: 2222 },
     *           {documentNodeName: 'PurchaseOrder', document: 220, line: 1111 },
     *           {documentNodeName: 'PurchaseReceipt', document: 330, line: 3322 },
     *           {documentNodeName: 'PurchaseReceipt', document: 330, line: 3333 },
     *  --> [{documentNodeName: 'PurchaseOrder', documents: [110, 220], lines: [1111, 5555, 2222]},
     *      {documentNodeName: 'PurchaseReceipt', documents: [330], lines: [3322, 3333]}]
     */
    getLinkedLinesGroups(): xtremMasterData.interfaces.GroupOfLinesToNotify[] {
        const groups = _.groupBy(this.linesToNotify, 'documentNodeName');
        return Object.keys(groups).map(documentNodeName => ({
            documentNodeName,
            documentIds: _.uniq(groups[documentNodeName].map(line => line.documentId)),
            lineIds: _.uniq(groups[documentNodeName].flatMap(line => line.lineId)),
        }));
    }

    async updateLinkedDocument() {
        // 1- group linesToNotify by node name
        const groups = this.getLinkedLinesGroups();
        // 2- notify for each constructor
        await xtremMasterData.functions.lineToLineLib.asyncUpdateStatuses(this.$.context, groups);
    }

    updateDocumentLineTo(
        lines:
            | Collection<xtremMasterData.nodes.BaseDocumentItemLine>
            | AsyncArrayReader<xtremMasterData.nodes.BaseDocumentItemLine>,
    ): Promise<boolean> {
        _.noop(this);
        _.noop(lines);
        return Promise.resolve(false);
    }

    @decorators.asyncMutation<typeof BaseDocument, 'updateLineTo'>({
        parameters: [
            {
                name: 'documentIds',
                type: 'array',
                item: {
                    type: 'integer',
                },
            },
            {
                name: 'documentLineIds',
                type: 'array',
                item: {
                    type: 'integer',
                },
            },
        ],
        return: {
            type: 'boolean',
        },
    })
    static async updateLineTo(
        writableContext: Context,
        documentIds: integer[],
        documentLineIds: integer[],
    ): Promise<boolean> {
        logger.debug(
            () =>
                `asyncMutation BaseDocument updateLineTo documentIds=${documentIds} documentLineIds=${documentLineIds}`,
        );

        const documents = writableContext.query(BaseDocument, {
            filter: { _id: { _in: _.uniq(documentIds) } },
            forUpdate: true,
        });
        const uniqOrderLineIds = _.uniq(documentLineIds);
        await documents.forEach(async document => {
            const lines = uniqOrderLineIds?.length
                ? document.lines.filter(line => uniqOrderLineIds.includes(line._id))
                : document.lines;
            if (await document.updateDocumentLineTo(lines)) {
                await document.$.save({ deferred: true });
                logger.debug(() => `${document.$.factory.name} updated`);
            }
        });
        return true;
    }

    // eslint-disable-next-line class-methods-use-this
    resynchronize(): Promise<boolean> {
        return Promise.resolve(true);
    }

    @decorators.bulkMutation<typeof BaseDocument, 'bulkResync'>({
        isPublished: true,
    })
    static bulkResync(_context: Context, document: BaseDocument): Promise<boolean> {
        return document.resynchronize();
    }
}
