# BillOfMaterial Bulk Mutation Readonly Context Error Analysis

## Executive Summary

The xtrem system is experiencing a critical error during BillOfMaterial bulk print operations where the context becomes readonly when attempting to create BillOfMaterialPrintout objects. This results in a "context is readonly" error that prevents bulk printing functionality from working correctly.

**Error**: `BillOfMaterialPrintout: cannot create: context is readonly`

**Impact**: Complete failure of BOM bulk print operations, affecting user workflows and reporting capabilities.

**Root Cause**: The bulk mutation is configured with `startsReadOnly: true` but attempts to create BillOfMaterialPrintout objects in a readonly context without properly switching to a writable context when needed.

## Technical Analysis

### Error Flow Analysis

The error occurs in the following sequence:

1. **Bulk Print Initiation**: User triggers bulk print via `BillOfMaterial.bulkPrint()` method
2. **Readonly Context**: The bulk mutation decorator has `startsReadOnly: true` (line 426 in bill-of-material.ts)
3. **Report Generation**: `generateBomReport()` is called which attempts to create a BillOfMaterialPrintout
4. **Context Creation Failure**: `context.create()` is called in a readonly context (line 140 in bill-of-material-printout.ts)
5. **Validation Error**: `NodeFactory.checkCanCreate()` validates context writability and throws the error

### Key Code Locations

**BillOfMaterial.bulkPrint() - lines 423-462**

```typescript
@decorators.bulkMutation<typeof BillOfMaterial, 'bulkPrint'>({
    isPublished: true,
    queue: 'reporting',
    startsReadOnly: true,  // ← This causes the readonly context
    // ...
})
static async bulkPrint(context: Context, bom: BillOfMaterial, ...) {
    const report = await xtremTechnicalData.nodes.BillOfMaterialPrintout.generateBomReport(
        context,  // ← Readonly context passed here
        bom._id.toString(),
        // ...
    );
}
```

**BillOfMaterialPrintout.createBomPrintout() - lines 134-154**

```typescript
static async createBomPrintout(context: Context, ...) {
    const bomPrintout = await context.create(xtremTechnicalData.nodes.BillOfMaterialPrintout, {
        // ← This fails because context is readonly
    });
}
```

**NodeFactory.checkCanMutate() - lines 2235-2250**

```typescript
checkCanMutate(context: Context, verb: string): void {
    if (
        (!context.isWritable || context.hasReadonlyScopes()) &&
        (this.storage === 'sql' || this.storage === 'external')
    ) {
        throw this.systemError(`cannot ${verb}: context is readonly`);  // ← Error thrown here
    }
}
```

### Historical Context

Based on git commit history, this is a known pattern that has been addressed in other modules:

1. **Commit b70f669**: Introduced `startsReadOnly: true` for bulk print operations to route them to the reporting queue
2. **Commit 3cb876d**: Fixed similar issues in sales modules by using `context.runInWritableContext()` for operations requiring write access
3. **Commit c29c9a5e**: Fixed supply chain bulk operations using writable contexts for database updates

## Root Cause Analysis

The fundamental issue is an architectural mismatch:

1. **Design Intent**: Bulk print operations should start readonly for performance and safety
2. **Implementation Reality**: The BillOfMaterialPrintout creation process requires write access to create temporary objects
3. **Missing Pattern**: Unlike other modules (sales, supply chain), the technical data module hasn't implemented the writable context pattern for necessary write operations

### Context State Management

The system uses a sophisticated context hierarchy:

- **Readonly Context**: Prevents mutations, used for safety in bulk operations
- **Writable Context**: Allows mutations, required for object creation
- **Child Context**: Can be created with different permissions than parent

The error occurs because:

1. Bulk mutation starts with readonly context (`startsReadOnly: true`)
2. `generateBomReport()` needs to create BillOfMaterialPrintout objects
3. No mechanism exists to switch to writable context for this specific operation

## Solution Architecture

### Recommended Approach

Follow the established pattern from sales and supply chain modules by implementing selective writable context usage:

1. **Keep `startsReadOnly: true`** for the bulk mutation decorator (maintains performance benefits)
2. **Wrap write operations** in `context.runInWritableContext()` calls
3. **Maintain readonly context** for read operations and report generation

### Implementation Strategy

**Phase 1: Immediate Fix**

- Modify `BillOfMaterialPrintout.createBomPrintout()` to use writable context
- Ensure `generateBomReport()` handles context switching appropriately

**Phase 2: Optimization**

- Review and optimize the temporary object creation pattern
- Consider if BillOfMaterialPrintout objects need to be persisted or can be transient

**Phase 3: Testing**

- Comprehensive testing of bulk print operations
- Verify single print operations remain unaffected
- Performance testing to ensure readonly benefits are maintained

### Detailed Code Changes Required

**1. services/shared/xtrem-technical-data/lib/nodes/bill-of-material-printout.ts**

Modify the `createBomPrintout()` method to use writable context:

```typescript
static async createBomPrintout(
    context: Context,
    billOfMaterial: xtremTechnicalData.nodes.BillOfMaterial,
    singleLevel = false,
    baseQuantity?: decimal,
): Promise<xtremTechnicalData.nodes.BillOfMaterialPrintout> {
    const createInWritableContext = async (writableContext: Context) => {
        const bomPrintout = await writableContext.create(xtremTechnicalData.nodes.BillOfMaterialPrintout, {
            id: writableContext.batch.trackingId,
            billOfMaterial,
            components: await BillOfMaterialPrintout.bomMultiLevelComponents(
                context, // Use original context for read operations
                billOfMaterial,
                baseQuantity || (await billOfMaterial.baseQuantity),
                singleLevel,
            ),
        });

        await bomPrintout.$.save();
        return bomPrintout;
    };

    if (context.isWritable) {
        return createInWritableContext(context);
    } else {
        return context.runInWritableContext(createInWritableContext);
    }
}
```

**2. Alternative Approach: Modify generateBomReport()**

If the above approach has issues, modify `generateBomReport()` to handle context switching:

```typescript
static async generateBomReport(
    context: Context,
    bomId: string,
    singleLevel = false,
    omitCost = false,
    omitInstruction = false,
    skipReport = false,
    skipDelete = false,
    baseQuantity?: decimal,
): Promise<xtremUpload.nodes.UploadedFile | null> {
    context.logger.info(`BOM printout mutation has started for id ${bomId}`);

    const bom = await context.read(xtremTechnicalData.nodes.BillOfMaterial, { _id: bomId });

    let bomName = await bom.name;
    if (bomName.length === 0) bomName = await (await bom.item).name;
    await context.batch.logMessage('info', bomName);
    await context.batch.logMessage('info', 'Mutation start');

    // Use writable context for BOM printout creation
    const bomPrintout = await BillOfMaterialPrintout.createBomPrintout(
        context,
        bom,
        singleLevel,
        baseQuantity || (await bom.baseQuantity),
    );

    await context.batch.logMessage('info', 'BOM printout data created');

    let report = null;
    if (!skipReport) {
        report = await xtremReporting.nodes.Report.generateUploadedFile(context, 'bomMultiLevel', '', {
            variables: JSON.stringify({
                bomPrintoutId: await bomPrintout.id,
                singleLevel: singleLevel ? 'true' : 'false',
                omitCost: omitCost ? 'true' : 'false',
                omitInstruction: omitInstruction ? 'true' : 'false',
            }),
        });
        await context.batch.logMessage('result', 'BOM printout data reported', { data: report });
    }

    if (!skipDelete) {
        // Use writable context for deletion as well
        const deleteInWritableContext = async (writableContext: Context) => {
            const printoutToDelete = await writableContext.read(
                xtremTechnicalData.nodes.BillOfMaterialPrintout,
                { id: await bomPrintout.id }
            );
            await printoutToDelete.$.delete();
        };

        if (context.isWritable) {
            await deleteInWritableContext(context);
        } else {
            await context.runInWritableContext(deleteInWritableContext);
        }

        await context.batch.logMessage('info', 'BOM printout data deleted');
    }

    return report;
}
```

### Testing Strategy

**Unit Tests**

- Add test cases for bulk print operations in readonly contexts
- Verify context state management during operations
- Test error scenarios and rollback behavior

**Integration Tests**

- Test complete bulk print workflow from UI to report generation
- Verify single print operations remain unaffected
- Performance testing to ensure readonly benefits are maintained

**Test Implementation Example**

```typescript
describe('BillOfMaterial bulk print with readonly context', () => {
    it('should handle bulk print in readonly context', () =>
        Test.withContext(async context => {
            // Test that bulk print works with readonly context
            const bom = await context.read(xtremTechnicalData.nodes.BillOfMaterial, { _id: 'test-bom-id' });

            // This should not throw "context is readonly" error
            const result = await xtremTechnicalData.nodes.BillOfMaterial.bulkPrint(
                context,
                bom,
                true, // multilevel
                false, // cost
                false, // instruction
            );

            assert.isNotNull(result);
        }));
});
```

### Risk Assessment

**Low Risk Changes**:

- Following established patterns from other modules (sales, supply chain)
- Minimal impact on existing functionality
- Clear rollback path available

**Potential Risks**:

- Performance impact if writable context is overused
- Transaction management complexity
- Potential for context leaks if not properly managed

**Mitigation Strategies**:

- Use writable context only for necessary write operations
- Maintain readonly context for all read operations
- Implement proper error handling and context cleanup
- Comprehensive testing before deployment

### Success Criteria

1. **Functional**: Bulk print operations complete without readonly context errors
2. **Compatibility**: Single print operations continue to work correctly
3. **Performance**: Performance characteristics remain acceptable
4. **Reliability**: No regression in other BOM-related functionality
5. **Maintainability**: Proper error handling and logging maintained

### Implementation Tasks

1. **Immediate (Priority 1)**
    - [x] Modify `BillOfMaterialPrintout.createBomPrintout()` method
    - [x] Update `generateBomReport()` if needed
    - [ ] Add unit tests for the changes

2. **Short-term (Priority 2)**
    - [ ] Integration testing with full bulk print workflow
    - [ ] Performance validation
    - [ ] Code review and documentation updates

3. **Long-term (Priority 3)**
    - [ ] Review other technical data bulk operations for similar issues
    - [ ] Document the pattern for future bulk operation implementations
    - [ ] Consider architectural improvements for context management

## Next Steps

1. **Implement the fix** following the sales module pattern in `createBomPrintout()`
2. **Test thoroughly** in development environment with both single and bulk operations
3. **Performance validation** to ensure readonly benefits are preserved
4. **Deploy with monitoring** to catch any edge cases
5. **Document the pattern** for future bulk operation implementations

This solution addresses the immediate issue while maintaining the architectural benefits of readonly bulk operations and following established patterns in the codebase.

## Fix Implementation and Resolution

The proposed solution was implemented to resolve the readonly context error. During the implementation, an additional bug was identified and fixed.

### Initial Compilation Error

Upon attempting to fix the readonly context issue, a compilation error was encountered in `/workspaces/xtrem/services/shared/xtrem-technical-data/lib/nodes/bill-of-material.ts`:

**Error**: `Type 'void[]' is not assignable to type '(UploadedFile | null)[]'.`

This error was caused by the `bulkPrint` function not returning a value, which was corrected by adding a `return await` statement.

### Implementation of Writable Context Handling

Following the plan outlined in this document, the following changes were made to `services/shared/xtrem-technical-data/lib/nodes/bill-of-material-printout.ts`:

1.  **`createBomPrintout()` Modification**: The `createBomPrintout` function was updated to check if the context is writable. If not, it uses `context.runInWritableContext` to create a writable context for creating the `BillOfMaterialPrintout` object. This makes the function robust and safe to call from a readonly context.

2.  **`generateBomReport()` Modification**: The deletion logic within `generateBomReport` was also updated to use `context.runInWritableContext` when the context is not writable. This ensures that the `bomPrintout` object can be successfully deleted even when the operation is initiated from a readonly context.

These changes align with the recommended approach and have resolved the "context is readonly" error, allowing the BillOfMaterial bulk print operations to complete successfully.